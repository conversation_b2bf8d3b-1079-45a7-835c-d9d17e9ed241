package com.wexl.pallavi.preprimary.repository;

import com.wexl.pallavi.dto.CompetenciesData;
import com.wexl.pallavi.preprimary.model.CompetenciesStudents;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CompetenciesStudentRepository extends JpaRepository<CompetenciesStudents, Long> {
  @Query(
      value =
          """
                    select pppcs.student_id as studentId,pppcs.term1 as term1,pppcs.term2 as term2,pppc."name" as name,pppc.subject_name as subjectName,pppc.subject_slug as subjectSlug,pppc.skill as skill
                    from pallavi_pre_primary_competency_students pppcs
                    join pallavi_pre_primary_competencies pppc on pppc.id = pppcs.pallavi_pre_primary_competencies_id
                    where pppc.org_slug = :orgSlug and grade_slug = :gradeSlug and pppcs.student_id  = :studentId and pppc.subject_slug = :subjectSlug
                    """,
      nativeQuery = true)
  List<CompetenciesData> getCompetenciesByStudentAndGradeAndOrgAndSubject(
      Long studentId, String gradeSlug, String orgSlug, String subjectSlug);
}
