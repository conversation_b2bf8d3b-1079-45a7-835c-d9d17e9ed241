package com.wexl.dps.reportcard;

import com.wexl.dps.dto.GilcoHolisticReportModelDTO;
import com.wexl.pallavi.dto.CompetenciesData;
import com.wexl.pallavi.preprimary.repository.CompetenciesStudentRepository;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GillcoHolisticReportCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final CompetenciesStudentRepository competenciesStudentRepository;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHolisticReportCardHeader(user);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  private GilcoHolisticReportModelDTO.Body buildBody(User user, String slug) {
    var student = user.getStudentInfo();
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    Optional<StudentAttributeValueModel> house =
        reportCardService.getStudentAttributeValue(student, "house");
    Optional<StudentAttributeValueModel> address =
        reportCardService.getStudentAttributeValue(student, "residential_address");
    return GilcoHolisticReportModelDTO.Body.builder()
        .name(user.getFirstName() + " " + user.getLastName())
        .className(student.getSection().getGradeName())
        .rollNo(student.getClassRollNumber())
        .sectionName(student.getSection().getName())
        .admissionNumber(student.getRollNumber())
        .house(house.map(StudentAttributeValueModel::getValue).orElse(null))
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .address(address.map(StudentAttributeValueModel::getValue).orElse(null))
        .fatherName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .motherName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .term1(buildTerm1(student, slug))
        .term2(buildTerm2(student, slug))
        .build();
  }

  private GilcoHolisticReportModelDTO.Term2 buildTerm2(Student student, String orgSlug) {
    return GilcoHolisticReportModelDTO.Term2.builder()
        .languageAndLiteracy(buildLanguageAndLiteracy(student, orgSlug, "t2"))
        .cognitiveDevelopment(buildCognitiveDevelopment(student, orgSlug, "t2"))
        .environMentalAwareness(buildEnvironMentalAwareness(student, orgSlug, "t2"))
        .physicalDevelopment(buildPhysicalDevelopment(student, orgSlug, "t2"))
        .socioEmotionalDevelopment(buildSocioEmotionalDevelopment(student, orgSlug, "t2"))
        .learningSkills(buildLearningSkills(student, orgSlug, "t2"))
        .remarks("remarks")
        // .gradingSystem(buildGradingSystem(student, orgSlug))
        .build();
  }

  private List<GilcoHolisticReportModelDTO.SkillSubject> buildLearningSkills(
      Student student, String orgSlug, String termSlug) {
    var learningSkills =
        getCompetenciesByStudentAndGradeAndOrgAndSubject(student, orgSlug, "learning-skills");
    Map<String, List<CompetenciesData>> groupedBySubject =
        learningSkills.stream().collect(Collectors.groupingBy(CompetenciesData::getName));
    List<GilcoHolisticReportModelDTO.SkillSubject> learningSkillsList = new ArrayList<>();
    groupedBySubject.forEach(
        (subject, competenciesData) -> {
          learningSkillsList.add(
              GilcoHolisticReportModelDTO.SkillSubject.builder()
                  .subject(subject)
                  .skillValue(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Skill Value"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .build());
        });
    return learningSkillsList;
  }

  private List<GilcoHolisticReportModelDTO.SkillSubject> buildSocioEmotionalDevelopment(
      Student student, String orgSlug, String termSlug) {
    var socioEmotionalDevelopment =
        getCompetenciesByStudentAndGradeAndOrgAndSubject(
            student, orgSlug, "socio-emotional-development");
    Map<String, List<CompetenciesData>> groupedBySubject =
        socioEmotionalDevelopment.stream()
            .collect(Collectors.groupingBy(CompetenciesData::getName));
    List<GilcoHolisticReportModelDTO.SkillSubject> socioEmotionalDevelopmentList =
        new ArrayList<>();
    groupedBySubject.forEach(
        (subject, competenciesData) -> {
          socioEmotionalDevelopmentList.add(
              GilcoHolisticReportModelDTO.SkillSubject.builder()
                  .subject(subject)
                  .skillValue(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Skill Value"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .build());
        });
    return socioEmotionalDevelopmentList;
  }

  private GilcoHolisticReportModelDTO.PhysicalDevelopment buildPhysicalDevelopment(
      Student student, String orgSlug, String termSlug) {
    var physicalDevelopment =
        getCompetenciesByStudentAndGradeAndOrgAndSubject(student, orgSlug, "physical-development");
    Map<String, List<CompetenciesData>> groupedBySubject =
        physicalDevelopment.stream().collect(Collectors.groupingBy(CompetenciesData::getName));
    List<GilcoHolisticReportModelDTO.Table1> physicalSkillList = new ArrayList<>();
    List<GilcoHolisticReportModelDTO.Table2> physicalDevelopmentList = new ArrayList<>();
    groupedBySubject.forEach(
        (subject, competenciesData) -> {
          physicalSkillList.add(
              GilcoHolisticReportModelDTO.Table1.builder()
                  .subject(subject)
                  .creativity(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Creativity"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .aestheticValue(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Aesthetic Value"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .pincerGrip(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Pincer Grip"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .eyeHandCoordination(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Eye-Hand Coordination"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .eyeFootCoordination(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Eye-Foot Coordination"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .build());
        });
    groupedBySubject.forEach(
        (subject, competenciesData) -> {
          physicalDevelopmentList.add(
              GilcoHolisticReportModelDTO.Table2.builder()
                  .subject(subject)
                  .balanceOrCoordination(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Balance or Coordination"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .locomotorSkills(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Locomotor Skills"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .manipulativeSkills(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Manipulative Skills"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .physicalStrength(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Physical Strength"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .heightAndWeight(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Height and Weight"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .build());
        });

    return GilcoHolisticReportModelDTO.PhysicalDevelopment.builder()
        .table1(physicalSkillList)
        .table2(physicalDevelopmentList)
        .build();
  }

  private List<GilcoHolisticReportModelDTO.EnvironmentalAwareness> buildEnvironMentalAwareness(
      Student student, String orgSlug, String termSlug) {
    var environMentalAwareness =
        getCompetenciesByStudentAndGradeAndOrgAndSubject(
            student, orgSlug, "environmental-awareness");
    Map<String, List<CompetenciesData>> groupedBySubject =
        environMentalAwareness.stream().collect(Collectors.groupingBy(CompetenciesData::getName));
    List<GilcoHolisticReportModelDTO.EnvironmentalAwareness> environMentalAwarenessList =
        new ArrayList<>();
    groupedBySubject.forEach(
        (subject, competenciesData) -> {
          environMentalAwarenessList.add(
              GilcoHolisticReportModelDTO.EnvironmentalAwareness.builder()
                  .themes(subject)
                  .knowledge(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Knowledge"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .attitude(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Attitude"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .application(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Application"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .build());
        });
    return environMentalAwarenessList;
  }

  private List<GilcoHolisticReportModelDTO.CognitiveDevelopment> buildCognitiveDevelopment(
      Student student, String orgSlug, String termSlug) {
    var cognitiveDevelopment =
        getCompetenciesByStudentAndGradeAndOrgAndSubject(student, orgSlug, "cognitive-development");
    Map<String, List<CompetenciesData>> groupedBySubject =
        cognitiveDevelopment.stream().collect(Collectors.groupingBy(CompetenciesData::getName));
    List<GilcoHolisticReportModelDTO.CognitiveDevelopment> cognitiveDevelopmentList =
        new ArrayList<>();
    groupedBySubject.forEach(
        (subject, competenciesData) -> {
          cognitiveDevelopmentList.add(
              GilcoHolisticReportModelDTO.CognitiveDevelopment.builder()
                  .mathematical(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Mathematical"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .knowledge(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Knowledge"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .computingSkills(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Computing Skills"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .application(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Application"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .build());
        });
    return cognitiveDevelopmentList;
  }

  private GilcoHolisticReportModelDTO.Term1 buildTerm1(Student student, String orgSlug) {
    return GilcoHolisticReportModelDTO.Term1.builder()
        .languageAndLiteracy(buildLanguageAndLiteracy(student, orgSlug, "t1"))
        .cognitiveDevelopment(buildCognitiveDevelopment(student, orgSlug, "t1"))
        .environMentalAwareness(buildEnvironMentalAwareness(student, orgSlug, "t1"))
        .physicalDevelopment(buildPhysicalDevelopment(student, orgSlug, "t1"))
        .socioEmotionalDevelopment(buildSocioEmotionalDevelopment(student, orgSlug, "t1"))
        .learningSkills(buildLearningSkills(student, orgSlug, "t1"))
        .remarks("remarks")
        // .gradingSystem(buildGradingSystem(student, orgSlug))
        .build();
  }

  private List<GilcoHolisticReportModelDTO.LanguageAndLiteracy> buildLanguageAndLiteracy(
      Student student, String orgSlug, String termSlug) {
    var competencies =
        getCompetenciesByStudentAndGradeAndOrgAndSubject(
            student, orgSlug, "language-and-literacy-development");
    Map<String, List<CompetenciesData>> groupedBySubject =
        competencies.stream().collect(Collectors.groupingBy(CompetenciesData::getName));
    List<GilcoHolisticReportModelDTO.LanguageAndLiteracy> languageAndLiteracyList =
        new ArrayList<>();
    groupedBySubject.forEach(
        (subject, competenciesData) -> {
          languageAndLiteracyList.add(
              GilcoHolisticReportModelDTO.LanguageAndLiteracy.builder()
                  .subject(subject)
                  .listening(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Listening"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .speaking(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Speaking"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .reading(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Reading"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .writing(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Writing"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .vocabulary(
                      competenciesData.stream()
                          .filter(cd -> cd.getSkill().equalsIgnoreCase("Vocabulary"))
                          .findFirst()
                          .map(
                              cd -> "t1".equalsIgnoreCase(termSlug) ? cd.getTerm1() : cd.getTerm2())
                          .orElse(null))
                  .build());
        });

    return languageAndLiteracyList;
  }

  private List<CompetenciesData> getCompetenciesByStudentAndGradeAndOrgAndSubject(
      Student student, String orgSlug, String subjectSlug) {
    return competenciesStudentRepository.getCompetenciesByStudentAndGradeAndOrgAndSubject(
        student.getId(), student.getSection().getGradeSlug(), orgSlug, subjectSlug);
  }

  private GilcoHolisticReportModelDTO.Header buildHolisticReportCardHeader(User user) {
    return GilcoHolisticReportModelDTO.Header.builder()
        .imageUrl("HOLISTIC PROGRESS REPORT CARD 2024-25")
        .build();
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("Gilco-holistic-report-card.xml");
  }
}
